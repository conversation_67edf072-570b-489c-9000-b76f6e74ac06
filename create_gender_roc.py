#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create gender-stratified ROC curves for the STOP-BANG model.
"""

import pandas as pd
from osa_analysis.data.loader import DataLoader
from osa_analysis.analysis.stop_bang import StopBangAnalyzer
from osa_analysis.analysis.statistics import StatisticsGenerator

def main():
    """Create gender-stratified ROC curves for STOP-BANG model."""
    
    # Load the data
    print("Loading data...")
    loader = DataLoader()
    df = loader.load_and_merge_data()
    
    # Calculate STOP-BANG scores
    print("Calculating STOP-BANG scores...")
    stop_bang_analyzer = StopBangAnalyzer()
    stop_bang_df = stop_bang_analyzer.analyze(df)
    
    # Create gender-stratified ROC curves
    print("Creating gender-stratified ROC curves...")
    stats_generator = StatisticsGenerator()
    
    # Generate ROC curve for STOP-BANG model
    gender_metrics = stats_generator.create_gender_stratified_roc_curve(
        df=stop_bang_df,
        model_column='partial_stop_bang_score',
        save_path='plots/stop_bang_gender_roc.png'
    )
    
    print("\nAnalysis complete!")
    print("Check the 'plots' directory for the generated ROC curve.")

if __name__ == "__main__":
    main()
